<?php
/**
 * Permission Functions
 * Functions for checking user permissions
 */

/**
 * Check if user has a specific permission
 * Admin users have all permissions by default
 * Other user types are checked against the database
 */
function hasPermission($permission_name, $module = null, $conn = null) {
    // Check if user is logged in
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_type'])) {
        return false;
    }
    
    $user_type = $_SESSION['user_type'];
    
    // Admin has all permissions
    if ($user_type === 'Admin') {
        return true;
    }
    
    // For other user types, check database
    if ($conn === null) {
        global $conn;
        if (!$conn) {
            return false;
        }
    }
    
    try {
        if ($module) {
            // Check specific module permission
            $stmt = $conn->prepare("SELECT COUNT(*) FROM permissions WHERE user_type = ? AND module = ? AND permission_name = ? AND status = 'active'");
            $stmt->execute([$user_type, $module, $permission_name]);
        } else {
            // Check permission without module
            $stmt = $conn->prepare("SELECT COUNT(*) FROM permissions WHERE user_type = ? AND permission_name = ? AND status = 'active'");
            $stmt->execute([$user_type, $permission_name]);
        }
        
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        error_log("Permission check error: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if user can access a specific module
 */
function canAccessModule($module, $conn = null) {
    // Check if user is logged in
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_type'])) {
        return false;
    }
    
    $user_type = $_SESSION['user_type'];
    
    // Admin can access all modules
    if ($user_type === 'Admin') {
        return true;
    }
    
    // For other user types, check if they have any permission in the module
    if ($conn === null) {
        global $conn;
        if (!$conn) {
            return false;
        }
    }
    
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM permissions WHERE user_type = ? AND module = ? AND status = 'active'");
        $stmt->execute([$user_type, $module]);
        
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        error_log("Module access check error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get all permissions for a user type
 */
function getUserPermissions($user_type, $conn = null) {
    // Admin has all permissions
    if ($user_type === 'Admin') {
        return 'all'; // Special value indicating all permissions
    }
    
    if ($conn === null) {
        global $conn;
        if (!$conn) {
            return [];
        }
    }
    
    try {
        $stmt = $conn->prepare("SELECT module, permission_name FROM permissions WHERE user_type = ? AND status = 'active'");
        $stmt->execute([$user_type]);
        
        $permissions = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $permissions[$row['module']][] = $row['permission_name'];
        }
        
        return $permissions;
    } catch (Exception $e) {
        error_log("Get user permissions error: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if current user is admin
 */
function isAdmin() {
    return isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'Admin';
}

/**
 * Get user type display name
 */
function getUserTypeDisplayName($user_type) {
    $display_names = [
        'Admin' => 'Administrator',
        'Secretary' => 'Secretary',
        'Chairman' => 'Barangay Chairman',
        'Kagawad' => 'Barangay Kagawad',
        'Staff' => 'Staff Member'
    ];
    
    return isset($display_names[$user_type]) ? $display_names[$user_type] : $user_type;
}

/**
 * Redirect if user doesn't have permission
 */
function requirePermission($permission_name, $module = null, $redirect_url = '../index.php') {
    if (!hasPermission($permission_name, $module)) {
        $_SESSION['error'] = 'You do not have permission to access this page.';
        header("Location: $redirect_url");
        exit;
    }
}

/**
 * Redirect if user is not admin
 */
function requireAdmin($redirect_url = '../index.php') {
    if (!isAdmin()) {
        $_SESSION['error'] = 'You do not have permission to access this page.';
        header("Location: $redirect_url");
        exit;
    }
}

/**
 * Get accessible modules for current user
 */
function getAccessibleModules($conn = null) {
    if (!isset($_SESSION['user_type'])) {
        return [];
    }
    
    $user_type = $_SESSION['user_type'];
    
    // Admin can access all modules
    if ($user_type === 'Admin') {
        return [
            'dashboard', 'residents', 'households', 'officials', 'documents', 
            'complaints', 'social', 'health', 'finance', 'properties', 
            'projects', 'governance', 'disaster', 'events', 'reports', 
            'settings', 'users', 'admin'
        ];
    }
    
    if ($conn === null) {
        global $conn;
        if (!$conn) {
            return [];
        }
    }
    
    try {
        $stmt = $conn->prepare("SELECT DISTINCT module FROM permissions WHERE user_type = ? AND status = 'active'");
        $stmt->execute([$user_type]);
        
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    } catch (Exception $e) {
        error_log("Get accessible modules error: " . $e->getMessage());
        return [];
    }
}

/**
 * Check if user can perform CRUD operations
 */
function canCreate($module, $conn = null) {
    return hasPermission('add_' . substr($module, 0, -1), $module, $conn) || 
           hasPermission('create_' . substr($module, 0, -1), $module, $conn);
}

function canEdit($module, $conn = null) {
    return hasPermission('edit_' . substr($module, 0, -1), $module, $conn) || 
           hasPermission('update_' . substr($module, 0, -1), $module, $conn);
}

function canDelete($module, $conn = null) {
    return hasPermission('delete_' . substr($module, 0, -1), $module, $conn);
}

function canView($module, $conn = null) {
    return hasPermission('view_' . $module, $module, $conn);
}

?>
