<?php
/**
 * Project Structure Cleanup Script
 * This script organizes debug files and removes unnecessary backup files
 * 
 * IMPORTANT: Review this script before running it!
 * It will move files and could break existing scripts that depend on current paths.
 */

echo "<h2>🧹 Project Structure Cleanup</h2>";
echo "<p><strong>WARNING:</strong> This script will reorganize your project structure. Review carefully before proceeding!</p>";

// Define cleanup actions
$cleanup_actions = [
    'move_to_debug' => [
        // Root level debug/test/check files to move to debug_files/
        'check_candidates_table.php',
        'check_complaints_blotter_issues.php',
        'check_db_structure.php',
        'check_delete_functionality.php',
        'check_document_requests.php',
        'check_document_requests_columns.php',
        'check_document_settings.php',
        'check_notifications_table.php',
        'check_qr_table.php',
        'check_resident_accounts.php',
        'check_resident_schema.php',
        'check_settings.php',
        'check_table.php',
        'check_table_structure.php',
        'check_triggers.php',
        'debug_activity_logs.php',
        'debug_document_request.php',
        'debug_notifications.php',
        'debug_system.php',
        'diagnose_activity_display.php',
        'final_certificate_print_test.php',
        'final_logactivity_scanner.php',
        'final_test.php',
        'test_activity_logging.php',
        'test_admin_document_notifications.php',
        'test_admin_notifications.php',
        'test_all_complaints_fixes.php',
        'test_backup_settings.php',
        'test_backup_settings_fix.php',
        'test_certificate_print.php',
        'test_complaints_functions.php',
        'test_disaster_and_export_fixes.php',
        'test_document_request.php',
        'test_document_status_notifications.php',
        'test_document_submission.php',
        'test_download_logging.php',
        'test_notification_triggers.php',
        'test_permissions_final_fix.php',
        'test_permissions_integration.php',
        'test_permissions_syntax.php',
        'test_permissions_user_types_fix.php',
        'test_print_logging.php',
        'test_template_logging.php',
        'verify_certificate_print_logging.php',
        'path_debug.php',
        'quick_test_print_logging.php'
    ],
    
    'move_to_maintenance' => [
        // Maintenance/fix scripts to move to maintenance/
        'fix_all_issues.php',
        'fix_all_logactivity_calls.php',
        'fix_candidates_table.php',
        'fix_database_schema.php',
        'fix_document_requests_schema.php',
        'fix_elections_candidates.php',
        'fix_email_password.php',
        'fix_governance_module.php',
        'fix_hearing_system.php',
        'fix_households_status.php',
        'fix_logging_functions.php',
        'fix_notifications_structure.php',
        'fix_official_roles.php',
        'fix_officials_creation.php',
        'fix_officials_table.php',
        'fix_permissions_user_type.php',
        'fix_resident_portal_integration.php',
        'fix_resident_portal_integration_modified.php',
        'fix_roles_all.php',
        'fix_schema_errors.php',
        'fix_twilio_config.php',
        'fix_users_table.php',
        'comprehensive_fix.php',
        'direct_permissions_fix.php',
        'direct_schema_fix.php',
        'direct_sql_fix.php',
        'execute_fix.php',
        'sidebar-fixer.php'
    ],
    
    'remove_backup_files' => [
        // Backup files to remove (be careful!)
        'resident_portal/request_document.php.bak',
        'resident_portal/request_document.php.bak.2',
        'includes/sidebar.php.backup',
        'modules/reports/case_reports.php.bak',
        'modules/residents/add_household.php.bak',
        'modules/documents/add_document.php.bak'
    ],
    
    'organize_admin_debug' => [
        // Admin debug files to move to admin/debug/
        'admin/debug_template_generation.php',
        'admin/test_backup_page.php',
        'admin/test_placeholder_replacement.php',
        'admin/test_template_flow.php',
        'admin/test_template_upload.php',
        'admin/test_template_with_placeholders.php',
        'admin/simple_template_test.php',
        'admin/check_email_settings.php',
        'admin/check_permissions_tables.php'
    ]
];

// Display what will be done
echo "<h3>📋 Cleanup Plan:</h3>";

echo "<h4>🔧 Files to move to debug_files/:</h4>";
echo "<ul>";
foreach ($cleanup_actions['move_to_debug'] as $file) {
    $exists = file_exists($file) ? "✅" : "❌";
    echo "<li>$exists $file</li>";
}
echo "</ul>";

echo "<h4>🛠️ Files to move to maintenance/:</h4>";
echo "<ul>";
foreach ($cleanup_actions['move_to_maintenance'] as $file) {
    $exists = file_exists($file) ? "✅" : "❌";
    echo "<li>$exists $file</li>";
}
echo "</ul>";

echo "<h4>🗑️ Backup files to remove:</h4>";
echo "<ul>";
foreach ($cleanup_actions['remove_backup_files'] as $file) {
    $exists = file_exists($file) ? "⚠️" : "✅ Already removed";
    echo "<li>$exists $file</li>";
}
echo "</ul>";

echo "<h4>📁 Admin debug files to organize:</h4>";
echo "<ul>";
foreach ($cleanup_actions['organize_admin_debug'] as $file) {
    $exists = file_exists($file) ? "✅" : "❌";
    echo "<li>$exists $file</li>";
}
echo "</ul>";

// Check if user wants to proceed
if (isset($_GET['execute']) && $_GET['execute'] === 'yes') {
    echo "<h3>🚀 Executing Cleanup...</h3>";
    
    // Create directories if they don't exist
    if (!is_dir('maintenance')) {
        mkdir('maintenance', 0755, true);
        echo "<p>✅ Created maintenance/ directory</p>";
    }
    
    if (!is_dir('admin/debug')) {
        mkdir('admin/debug', 0755, true);
        echo "<p>✅ Created admin/debug/ directory</p>";
    }
    
    $moved_count = 0;
    $removed_count = 0;
    $errors = [];
    
    // Move debug files
    foreach ($cleanup_actions['move_to_debug'] as $file) {
        if (file_exists($file)) {
            if (rename($file, 'debug_files/' . basename($file))) {
                echo "<p>✅ Moved $file to debug_files/</p>";
                $moved_count++;
            } else {
                $errors[] = "Failed to move $file";
            }
        }
    }
    
    // Move maintenance files
    foreach ($cleanup_actions['move_to_maintenance'] as $file) {
        if (file_exists($file)) {
            if (rename($file, 'maintenance/' . basename($file))) {
                echo "<p>✅ Moved $file to maintenance/</p>";
                $moved_count++;
            } else {
                $errors[] = "Failed to move $file";
            }
        }
    }
    
    // Move admin debug files
    foreach ($cleanup_actions['organize_admin_debug'] as $file) {
        if (file_exists($file)) {
            if (rename($file, 'admin/debug/' . basename($file))) {
                echo "<p>✅ Moved $file to admin/debug/</p>";
                $moved_count++;
            } else {
                $errors[] = "Failed to move $file";
            }
        }
    }
    
    // Remove backup files (be very careful!)
    foreach ($cleanup_actions['remove_backup_files'] as $file) {
        if (file_exists($file)) {
            if (unlink($file)) {
                echo "<p>🗑️ Removed backup file: $file</p>";
                $removed_count++;
            } else {
                $errors[] = "Failed to remove $file";
            }
        }
    }
    
    echo "<h3>📊 Cleanup Summary:</h3>";
    echo "<p>✅ Files moved: $moved_count</p>";
    echo "<p>🗑️ Files removed: $removed_count</p>";
    
    if (!empty($errors)) {
        echo "<h4>❌ Errors:</h4>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>$error</li>";
        }
        echo "</ul>";
    }
    
} else {
    echo "<h3>⚠️ Ready to Execute?</h3>";
    echo "<p><strong>IMPORTANT:</strong> This will move and delete files. Make sure you have a backup!</p>";
    echo "<p><a href='?execute=yes' style='background:#dc3545; color:white; padding:10px 20px; text-decoration:none; border-radius:5px;'>🚀 Execute Cleanup</a></p>";
    echo "<p><em>Or review the plan above and run individual commands manually.</em></p>";
}

echo "<h3>📝 Manual Cleanup Commands:</h3>";
echo "<p>If you prefer to run commands manually:</p>";
echo "<pre style='background:#f8f9fa; padding:10px; border-radius:5px;'>";
echo "# Create directories\n";
echo "mkdir -p maintenance admin/debug\n\n";
echo "# Move debug files\n";
echo "mv check_*.php debug_files/\n";
echo "mv test_*.php debug_files/\n";
echo "mv debug_*.php debug_files/\n\n";
echo "# Move maintenance files\n";
echo "mv fix_*.php maintenance/\n\n";
echo "# Remove backup files (be careful!)\n";
echo "rm *.bak *.backup\n";
echo "</pre>";

?>
