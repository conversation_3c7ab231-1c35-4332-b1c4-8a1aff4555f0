<?php
// Include session management
include '../../includes/session.php';

// Check if user is logged in
$is_logged_in = isset($_SESSION['user_id']) ? true : false;

// Page title
$page_title = "Residents Management";

// Database connection
$db = Database::getInstance();
$conn = $db->getConnection();
$db_connection_error = false;

// Check database connection
try {
    $conn->query("SELECT 1");
} catch (PDOException $e) {
    $db_connection_error = true;
}

// Clear any lingering error messages if this is a fresh page load (not an AJAX request)
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    if (isset($_SESSION['error'])) {
        unset($_SESSION['error']);
    }
}

// Check permission
if (!hasPermission('view_residents') && !$db_connection_error) {
    header("Location: ../../index.php");
    exit;
}

// Initialize default values for statistics
$residents_stats = [
    'total' => 0,
    'active' => 0,
    'inactive' => 0,
    'deceased' => 0,
    'voters' => 0,
    'seniors' => 0,
    'pwd' => 0
];

// Get residents with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$total_records = 0;
$total_pages = 0;
$residents = [];

// Get message from session
$message = isset($_SESSION['message']) ? $_SESSION['message'] : '';
$message_type = isset($_SESSION['message_type']) ? $_SESSION['message_type'] : 'info';

// Clear the session message
if(isset($_SESSION['message'])) {
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

if (!$db_connection_error) {
try {
    // Build SQL queries
    $sql_condition = "";
    $params = [];

    if (!empty($search)) {
        $sql_condition = " WHERE (
            first_name LIKE :search1 OR
            last_name LIKE :search2 OR
            middle_name LIKE :search3 OR
            address LIKE :search4
        )";
        $search_param = "%$search%";
        $params['search1'] = $search_param;
        $params['search2'] = $search_param;
        $params['search3'] = $search_param;
        $params['search4'] = $search_param;
    }

    if (!empty($status_filter)) {
        $sql_condition .= empty($sql_condition) ? " WHERE" : " AND";
        $sql_condition .= " status = :status";
        $params['status'] = $status_filter;
    }

    // Count query
    $count_sql = "SELECT COUNT(*) as total FROM residents" . $sql_condition;

    $count_stmt = $conn->prepare($count_sql);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue(":$key", $value);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $limit);

    // Main query
    $sql = "SELECT * FROM residents" . $sql_condition . "
           ORDER BY last_name, first_name
           LIMIT :offset, :limit";

    $stmt = $conn->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    $residents = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get statistics
    $stats_sql = "SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive,
        SUM(CASE WHEN status = 'deceased' THEN 1 ELSE 0 END) as deceased,
        SUM(CASE WHEN voter_status = 'Registered' THEN 1 ELSE 0 END) as voters,
        SUM(CASE WHEN is_senior = 1 THEN 1 ELSE 0 END) as seniors,
        SUM(CASE WHEN is_pwd = 1 THEN 1 ELSE 0 END) as pwd
        FROM residents";
    $stats_stmt = $conn->query($stats_sql);
    $residents_stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
        error_log("Error fetching residents: " . $e->getMessage());
        $_SESSION['error'] = 'A database error occurred. Please try again later.';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">

    <!-- Immediate fix for Active badges -->
    <style>
        /* Critical CSS for Active badges - loaded immediately */
        .btn-success,
        .active-badge,
        button.btn-success,
        button:contains('Active'),
        .btn-success.active-badge,
        button.active-badge {
            display: inline-block !important;
            background-color: #28a745 !important;
            color: white !important;
            border-color: #28a745 !important;
        }
    </style>

    <script>
        // Run this script immediately to fix any Active badges before page render
        document.addEventListener('DOMContentLoaded', function() {
            // Apply styles to all elements with Active text
            const styleSheet = document.createElement('style');
            styleSheet.innerHTML = `
                .custom-green-badge,
                span.badge:contains('Active'),
                div:contains('Active'),
                [class*="badge"]:contains('Active'),
                button:contains('Active'),
                .btn-success,
                .active-badge {
                    display: inline-block !important;
                    background-color: #28a745 !important;
                    color: white !important;
                    padding: 4px 10px !important;
                    border-radius: 5px !important;
                    border: 1px solid #28a745 !important;
                    font-size: 13px !important;
                    font-weight: 500 !important;
                }
            `;
            document.head.appendChild(styleSheet);

            // Force immediate styling of Active buttons
            setTimeout(function() {
                document.querySelectorAll('button').forEach(function(button) {
                    if (button.textContent && button.textContent.includes('Active')) {
                        button.className = 'btn btn-success btn-sm active-badge';
                        button.style.backgroundColor = '#28a745 !important';
                        button.style.color = 'white !important';
                        button.style.borderColor = '#28a745 !important';
                    }
                });
            }, 0);
        });
    </script>
    <style>
        .action-buttons .btn {
            margin: 0 2px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }

        .action-buttons .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .action-buttons .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }

        .action-buttons .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .action-buttons .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .action-buttons i {
            color: white;
        }

        /* Stat Card Styles */
        .stat-card {
            border-radius: 0.75rem;
            overflow: hidden;
            height: 100%;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            position: relative;
            z-index: 1;
        }
        .stat-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
            z-index: 2;
            cursor: pointer;
        }
        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }
        .stat-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        /* Border colors for different statuses */
        .border-primary { border-left: 4px solid #4e73df !important; }
        .border-success { border-left: 4px solid #1cc88a !important; }
        .border-info { border-left: 4px solid #36b9cc !important; }
        .border-warning { border-left: 4px solid #f6c23e !important; }
        .border-danger { border-left: 4px solid #e74a3b !important; }

        /* Background colors with opacity */
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }

        /* Card hover effects */
        .card.shadow {
            transition: all 0.3s ease;
        }
        .card.shadow:hover {
            box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15) !important;
            transform: translateY(-3px);
        }

        /* Table styles */
        .table-bordered-columns td, .table-bordered-columns th {
            border-left: 1px solid #e3e6f0;
            border-right: 1px solid #e3e6f0;
        }
        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.075);
        }
        .table th {
            font-weight: 500;
            background-color: #f8f9fc;
        }
        .action-column {
            width: 140px !important;
            min-width: 140px;
            text-align: center;
            vertical-align: middle;
            padding: 8px 4px !important;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: row;
            flex-wrap: nowrap;
            gap: 3px;
        }
        .action-buttons .btn {
            padding: 3px 6px;
            font-size: 12px;
            margin: 0;
            border-radius: 3px;
        }
        /* Fix table row height and spacing */
        .table tbody tr {
            height: auto;
        }
        .table tbody td {
            vertical-align: middle;
            padding: 8px 12px;
        }
        /* Status badge styles */
        .status-badge {
            display: inline-block !important;
            padding: 5px 10px !important;
            font-size: 13px !important;
            line-height: 1 !important;
            text-align: center !important;
            white-space: nowrap !important;
            vertical-align: middle !important;
            border-radius: 4px !important;
        }

        /* Force Bootstrap button colors */
        .btn-success, button.btn-success, .active-badge {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
            color: #fff !important;
        }
        .btn-warning, button.btn-warning {
            background-color: #ffc107 !important;
            border-color: #ffc107 !important;
            color: #212529 !important;
        }
        .btn-danger, button.btn-danger {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
            color: #fff !important;
        }

        /* Specific rule for Active status buttons in the table */
        td button.btn-success,
        td button.active-badge,
        td button:contains('Active') {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
            color: #fff !important;
        }

        /* Custom badge for active status */
        .custom-green-badge, .active-badge, button.btn-success {
            display: inline-block !important;
            background-color: #28a745 !important;
            color: white !important;
            padding: 4px 10px !important;
            border-radius: 5px !important;
            border: 1px solid #28a745 !important;
            font-size: 13px !important;
            font-weight: 500 !important;
        }

        /* Specific styling for active badges in the residents table */
        .btn-success.active-badge {
            background-color: #28a745 !important;
            color: white !important;
            border-color: #28a745 !important;
        }

        /* Ensure all buttons with Active text are green */
        button:contains('Active') {
            background-color: #28a745 !important;
            color: white !important;
            border-color: #28a745 !important;
        }

        /* Global rule to ensure all Active badges are green */
        span:contains('Active'), div:contains('Active') {
            background-color: #28a745 !important;
            color: white !important;
        }

        /* Ensure all elements with Active text have the right styling */
        [class*="badge"]:contains('Active'),
        [class*="status"]:contains('Active'),
        .badge:contains('Active') {
            background-color: #28a745 !important;
            color: white !important;
            border-color: #28a745 !important;
        }
    </style>
</head>
<body>
            <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>

            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">👨‍👩‍👧‍👦 Residents Management</h1>
                    <div class="btn-toolbar">
                        <?php if (!$db_connection_error && hasPermission('add_resident')): ?>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addResidentModal">
                            ➕ Add New Resident
                        </button>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($db_connection_error): ?>
                <!-- Database Error Message -->
                <div class="alert alert-danger">
                    <strong>⚠️ Database Connection Error:</strong> Could not connect to the database. Please contact the system administrator.
                </div>
                <?php else: ?>

                <!-- All session messages are now handled by toast notifications -->
                <!-- See the toast container at the bottom of the page -->

                <!-- Quick Statistics -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-primary">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-primary-soft text-primary">
                                            👥
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($residents_stats['total'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Total Residents</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-success">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-success-soft text-success">
                                            ✅
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($residents_stats['active'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Active Residents</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-warning">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-warning-soft text-warning">
                                            🗳️
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($residents_stats['voters'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Registered Voters</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-info">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-info-soft text-info">
                                            🧓
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($residents_stats['seniors'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Senior Citizens</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">🔍 Search and Filter</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                                <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">🔎</span>
                                    <input type="text" class="form-control" name="search" placeholder="Search by name, address..." value="<?php echo htmlspecialchars($search); ?>">
                                </div>
                                </div>
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">📊</span>
                                    <select class="form-select" name="status">
                                        <option value="">All Status</option>
                                        <option value="active" <?php echo ($status_filter == 'active') ? 'selected' : ''; ?>>Active</option>
                                        <option value="inactive" <?php echo ($status_filter == 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                        <option value="deceased" <?php echo ($status_filter == 'deceased') ? 'selected' : ''; ?>>Deceased</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="submit" class="btn btn-primary me-md-2">🔍 Filter</button>
                                    <a href="?" class="btn btn-secondary">🔄 Reset</a>
                                    <button type="button" class="btn btn-success" onclick="exportToExcel()">
                                        📊 Export
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Residents Table -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">📋 Residents List</h6>
                        <div class="dropdown no-arrow">
                            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
                                ⋮
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="dropdownMenuLink">
                                <li><a class="dropdown-item" href="?status=active">View Active</a></li>
                                <li><a class="dropdown-item" href="?status=inactive">View Inactive</a></li>
                                <li><a class="dropdown-item" href="?status=deceased">View Deceased</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="window.print(); return false;">Print List</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($residents)): ?>
                            <div class="alert alert-info mb-0">
                                <strong>🔍 No Results:</strong> No residents found matching your criteria. Try adjusting your search filters.
                            </div>
                        <?php else: ?>
                        <div class="table-responsive">
                                <table class="table table-hover table-bordered-columns" width="100%" cellspacing="0">
                                <thead class="table-light">
                                    <tr>
                                            <th width="3%">ID</th>
                                        <th width="18%">Name</th>
                                        <th width="25%">Address</th>
                                            <th width="8%">Gender</th>
                                            <th width="10%">Birth Date</th>
                                        <th width="10%">Contact</th>
                                        <th width="8%">Status</th>
                                            <th width="18%" class="action-column">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                        <?php foreach ($residents as $resident): ?>
                                            <tr>
                                            <td><?php echo $resident['resident_id']; ?></td>
                                                <td>
                                                    <small class="text-muted me-1">👤</small>
                                                    <?php
                                                    echo htmlspecialchars($resident['last_name'] . ', ' .
                                                         $resident['first_name'] . ' ' .
                                                         ($resident['middle_name'] ? $resident['middle_name'] : ''));
                                                    ?>
                                                </td>
                                                <td>
                                                    <small class="text-muted me-1">🏠</small>
                                                    <?php echo htmlspecialchars($resident['address']); ?>
                                                </td>
                                            <td>
                                                <small class="text-muted me-1">
                                                    <?php echo $resident['gender'] == 'Male' ? '♂️' : '♀️'; ?>
                                                </small>
                                                <?php echo htmlspecialchars($resident['gender']); ?>
                                            </td>
                                            <td>
                                                <small class="text-muted me-1">📅</small>
                                                <?php echo date('M d, Y', strtotime($resident['birthdate'])); ?>
                                            </td>
                                            <td>
                                                <small class="text-muted me-1">📞</small>
                                                <?php echo htmlspecialchars($resident['contact_number'] ?? ''); ?>
                                            </td>
                                            <td>
                                                <?php if ($resident['status'] == 'active'): ?>
                                                <button type="button" class="btn btn-success btn-sm active-badge">✅ Active</button>
                                                <?php elseif ($resident['status'] == 'inactive'): ?>
                                                <button type="button" class="btn btn-warning btn-sm">⚠️ Inactive</button>
                                                <?php else: ?>
                                                <button type="button" class="btn btn-danger btn-sm">⚠️ <?php echo ucfirst($resident['status']); ?></button>
                                                <?php endif; ?>
                                            </td>
                                            <td class="action-column">
                                                <div class="action-buttons">
                                                    <a href="direct_view.php?id=<?php echo $resident['resident_id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View Resident">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($is_logged_in || isset($_SESSION['user_id'])): ?>
                                                    <a href="direct_edit.php?id=<?php echo $resident['resident_id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit Resident">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="#" onclick="confirmDelete(<?php echo $resident['resident_id']; ?>)" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="Delete Resident">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            </tr>
                                        <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                            <?php if (isset($total_pages) && $total_pages > 1): ?>
                            <div class="d-flex justify-content-center mt-4">
                                <nav aria-label="Page navigation">
                                <ul class="pagination">
                                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($status_filter) ? '&status=' . urlencode($status_filter) : ''; ?>" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>

                                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                            <?php if ($i == 1 || $i == $total_pages || ($i >= $page - 1 && $i <= $page + 1)): ?>
                                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($status_filter) ? '&status=' . urlencode($status_filter) : ''; ?>">
                                                        <?php echo $i; ?>
                                                    </a>
                                                </li>
                                            <?php elseif ($i == 2 || $i == $total_pages - 1): ?>
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#">...</a>
                                        </li>
                                            <?php endif; ?>
                                    <?php endfor; ?>

                                        <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($status_filter) ? '&status=' . urlencode($status_filter) : ''; ?>" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                </ul>
                                </nav>
                            </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteModalLabel">⚠️ Confirm Delete</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this resident? This action cannot be undone.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">❌ Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDelete">🗑️ Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Resident Modal -->
    <div class="modal fade" id="addResidentModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addResidentModalLabel">➕ Add New Resident</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="addResidentForm" action="simplified_add_resident.php" method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="row">
                            <!-- Personal Information Section -->
                            <div class="col-md-12 mb-3">
                                <h6 class="fw-bold">👤 Personal Information</h6>
                                <hr>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="first_name" class="form-label">📝 First Name*</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="middle_name" class="form-label">📝 Middle Name</label>
                                <input type="text" class="form-control" id="middle_name" name="middle_name">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="last_name" class="form-label">📝 Last Name*</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                            </div>
                        </div>

                        <!-- Photo Upload Section -->

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="birthdate" class="form-label">📅 Birth Date*</label>
                                <input type="date" class="form-control" id="birthdate" name="birthdate" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="place_of_birth" class="form-label">🏙️ Place of Birth</label>
                                <input type="text" class="form-control" id="place_of_birth" name="place_of_birth">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="gender" class="form-label">⚧️ Gender*</label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="">Select Gender</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                </select>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="civil_status" class="form-label">Civil Status</label>
                                <select class="form-select" id="civil_status" name="civil_status">
                                    <option value="">Select Status</option>
                                    <option value="Single">Single</option>
                                    <option value="Married">Married</option>
                                    <option value="Widowed">Widowed</option>
                                    <option value="Separated">Separated</option>
                                    <option value="Divorced">Divorced</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="nationality" class="form-label">Nationality</label>
                                <input type="text" class="form-control" id="nationality" name="nationality" value="Filipino">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="religion" class="form-label">Religion</label>
                                <input type="text" class="form-control" id="religion" name="religion">
                            </div>

                            <!-- Profile Photo Section -->
                            <div class="col-md-8 mb-3">
                                <label for="profile_photo" class="form-label">Profile Photo</label>
                                <input type="file" class="form-control" id="profile_photo" name="profile_photo" accept="image/*">
                                <small class="text-muted">Max file size: 2MB. Supported formats: JPG, PNG</small>
                            </div>

                            <!-- Contact Information Section -->
                            <div class="col-md-12 mt-3 mb-3">
                                <h6 class="fw-bold">📞 Contact Information</h6>
                                <hr>
                            </div>

                            <div class="col-md-12 mb-3">
                                <label for="address" class="form-label">🏠 Address*</label>
                                <input type="text" class="form-control" id="address" name="address" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="contact_number" class="form-label">📱 Contact Number</label>
                                <input type="text" class="form-control" id="contact_number" name="contact_number">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">📧 Email</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>

                            <!-- Socioeconomic Information -->
                            <div class="col-md-12 mt-3 mb-3">
                                <h6 class="fw-bold">💼 Socioeconomic Information</h6>
                                <hr>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="occupation" class="form-label">Occupation</label>
                                <input type="text" class="form-control" id="occupation" name="occupation">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="educational_attainment" class="form-label">Educational Attainment</label>
                                <select class="form-select" id="educational_attainment" name="educational_attainment">
                                    <option value="">Select Education Level</option>
                                    <option value="None">None</option>
                                    <option value="Elementary">Elementary</option>
                                    <option value="High School">High School</option>
                                    <option value="Vocational">Vocational</option>
                                    <option value="College">College</option>
                                    <option value="Post Graduate">Post Graduate</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="monthly_income" class="form-label">Monthly Income</label>
                                <input type="number" class="form-control" id="monthly_income" name="monthly_income" step="0.01">
                            </div>

                            <!-- Barangay Record Information -->
                            <div class="col-md-12 mt-3 mb-3">
                                <h6 class="fw-bold">📋 Barangay Record Information</h6>
                                <hr>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="household_id" class="form-label">Household</label>
                                <select class="form-select" id="household_id" name="household_id">
                                    <option value="">None</option>
                                    <?php
                                    try {
                                        $household_query = "SELECT h.household_id, CONCAT(r.last_name, ' Household (', h.address, ')') AS household_name
                                                            FROM households h
                                                            LEFT JOIN household_members hm ON h.household_id = hm.household_id
                                                            LEFT JOIN residents r ON hm.resident_id = r.resident_id
                                                            WHERE hm.relationship = 'Head' OR hm.relationship = 'head'
                                                            ORDER BY household_name";
                                        $household_stmt = $conn->prepare($household_query);
                                        $household_stmt->execute();

                                        while ($household = $household_stmt->fetch(PDO::FETCH_ASSOC)) {
                                            echo "<option value=\"" . $household['household_id'] . "\">" . $household['household_name'] . "</option>";
                                        }
                                    } catch (PDOException $e) {
                                        // Log error but don't display
                                        error_log("Error fetching households: " . $e->getMessage());
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="voter_status" class="form-label">Voter Status</label>
                                <select class="form-select" id="voter_status" name="voter_status" required>
                                    <option value="Not Registered">Not Registered</option>
                                    <option value="Registered">Registered Voter</option>
                                </select>
                                <div class="invalid-feedback">Please select voter status.</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">📊 Resident Status*</label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="deceased">Deceased</option>
                                </select>
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="date_registered" class="form-label">Date Registered</label>
                                <input type="date" class="form-control" id="date_registered" name="date_registered" value="<?php echo date('Y-m-d'); ?>">
                            </div>

                            <!-- Special Categories -->
                            <div class="col-md-12 mt-3 mb-3">
                                <h6 class="fw-bold">🏷️ Special Categories</h6>
                                <hr>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_senior" name="is_senior" value="1">
                                    <label class="form-check-label" for="is_senior">🧓 Senior Citizen</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_pwd" name="is_pwd" value="1">
                                    <label class="form-check-label" for="is_pwd">♿ Person with Disability (PWD)</label>
                                </div>
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" id="is_solo_parent" name="is_solo_parent" value="1">
                                    <label class="form-check-label" for="is_solo_parent">👨‍👧 Solo Parent</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_indigenous" name="is_indigenous" value="1">
                                    <label class="form-check-label" for="is_indigenous">🏞️ Indigenous Person</label>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="pwd_type" class="form-label">PWD Type (if applicable)</label>
                                <select class="form-select" id="pwd_type" name="pwd_type">
                                    <option value="">Select PWD Type</option>
                                    <option value="Physical">Physical</option>
                                    <option value="Visual">Visual</option>
                                    <option value="Hearing">Hearing</option>
                                    <option value="Speech">Speech</option>
                                    <option value="Mental">Mental</option>
                                    <option value="Multiple">Multiple</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>

                            <!-- Hidden fields -->
                            <input type="hidden" name="qr_code" value="">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            ❌ Close
                        </button>
                        <button type="submit" class="btn btn-primary">
                            💾 Save Resident
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Toast container -->
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex align-items-center p-3">
                <div class="me-2" id="toast-icon">✅</div>
                <div class="me-auto" id="toast-message"></div>
                <div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
        // Handle delete confirmation
function confirmDelete(residentId) {
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            document.getElementById('confirmDelete').onclick = function() {
                window.location.href = 'delete_resident.php?id=' + residentId;
            };
            deleteModal.show();
        }

        // Handle view resident navigation
        function viewResident(residentId) {
            console.log("View function called for resident ID: " + residentId);
            window.location.href = 'view_resident.php?id=' + residentId;
            return false; // Prevent default anchor action
        }



        // Handle session messages auto dismiss
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM loaded - initializing");

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Remove any alert banners that might still be showing
            document.querySelectorAll('.alert-success, .alert-danger, .alert-warning, .alert-info').forEach(function(alert) {
                if (alert.closest('.toast-container') === null) {
                    // Only remove if it's not inside a toast container
                    alert.remove();
                }
            });

            // Auto dismiss alerts after 5 seconds
            setTimeout(function() {
                var alerts = document.querySelectorAll('.alert-dismissible');
                alerts.forEach(function(alert) {
                    var closeBtn = alert.querySelector('.btn-close');
                    if (closeBtn) {
                        closeBtn.click();
                    }
                });
            }, 5000);

            // Show toast notification if there's a message
            <?php if(!empty($message)): ?>
            const toast = document.getElementById('successToast');
            const toastMessage = document.getElementById('toast-message');
            const toastIcon = document.getElementById('toast-icon');

            // Set message
            toastMessage.textContent = "<?php echo addslashes($message); ?>";

            // Set styling based on message type
            <?php if($message_type == 'success'): ?>
            toast.style.backgroundColor = '#28a745';
            toastIcon.textContent = '✅';
            <?php elseif($message_type == 'danger' || $message_type == 'error'): ?>
            toast.style.backgroundColor = '#dc3545';
            toastIcon.textContent = '❌';
            <?php elseif($message_type == 'warning'): ?>
            toast.style.backgroundColor = '#ffc107';
            toast.style.color = '#000';
            toastIcon.textContent = '⚠️';
            <?php else: ?>
            toast.style.backgroundColor = '#17a2b8';
            toastIcon.textContent = '📝';
            <?php endif; ?>

            toast.style.color = <?php echo ($message_type == 'warning') ? "'#000'" : "'white'"; ?>;

            const successToast = new bootstrap.Toast(toast, {
                delay: 5000
            });
            successToast.show();
            <?php endif; ?>

            // Debugging for add resident form
            const addResidentForm = document.getElementById('addResidentForm');
            console.log("Add Resident Form:", addResidentForm);

            if (addResidentForm) {
                addResidentForm.addEventListener('submit', function(e) {
                    // Prevent default form submission to handle it manually
                    e.preventDefault();

                    console.log("Form submitted! Gathering form data...");

                    // Output all form fields
                    const formData = new FormData(addResidentForm);
                    console.log("Form data:");
                    for (const [key, value] of formData.entries()) {
                        console.log(`${key}: ${value}`);
                    }

                    // Manual submission of the form
                    const submitButton = addResidentForm.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.innerHTML = '⏳ Saving...';
                    }

                    // Submit the form manually
                    fetch(addResidentForm.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest' // Add this to identify as AJAX request
                        }
                    })
                    .then(response => {
                        console.log('Response status:', response.status);
                        console.log('Response headers:', response.headers);
                        return response.text();
                    })
                    .then(data => {
                        console.log('Response data:', data);

                        // Re-enable the submit button
                        if (submitButton) {
                            submitButton.disabled = false;
                            submitButton.innerHTML = '💾 Save Resident';
                        }

                        // Try to parse as JSON first
                        let success = false;
                        let message = '';

                        try {
                            // Try to parse JSON response
                            const jsonData = JSON.parse(data);
                            console.log('Parsed JSON response:', jsonData);

                            // Check if successful based on JSON
                            if (jsonData.success) {
                                success = true;
                                message = jsonData.message || 'Resident added successfully';
                            } else {
                                console.error('Error from server:', jsonData.message);
                                message = jsonData.message || 'Unknown error occurred';
                            }
                        } catch (e) {
                            // Not JSON, try to check if it contains success text
                            console.log('Response is not JSON, checking for success text');
                            if (data.includes('success') || data.includes('Resident added successfully')) {
                                success = true;
                                message = 'Resident added successfully';
                            } else {
                                console.error('Error adding resident (text response):', data);
                                message = 'Error adding resident. See console for details.';
                            }
                        }

                        if (success) {
                            console.log('Resident added successfully!');

                            // Close the modal
                            const modal = bootstrap.Modal.getInstance(document.getElementById('addResidentModal'));
                            if (modal) {
                                modal.hide();
                            }

                            // Use toast notification instead of alert banner
                            showToast(message, 'success');

                            // Scroll to top
                            window.scrollTo({ top: 0, behavior: 'smooth' });

                            // Reload page after showing the message for 2 seconds
                            setTimeout(() => {
                                window.location.reload();
                            }, 2000);
                        } else {
                            console.error('Error adding resident:', message);

                            // Use toast notification for error
                            showToast(message, 'danger');

                            // Scroll to top
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);

                        // Use toast notification for network error
                        showToast(`Network Error: ${error.message}`, 'danger');

                        // Re-enable the submit button
                        if (submitButton) {
                            submitButton.disabled = false;
                            submitButton.innerHTML = '💾 Save Resident';
                        }
                    });
                });
            }

            // Calculate age from birthdate
            const birthdateInput = document.getElementById('birthdate');
            if (birthdateInput) {
                birthdateInput.addEventListener('change', function() {
                    const birthdate = new Date(this.value);
                    const today = new Date();
                    let age = today.getFullYear() - birthdate.getFullYear();
                    const m = today.getMonth() - birthdate.getMonth();

                    if (m < 0 || (m === 0 && today.getDate() < birthdate.getDate())) {
                        age--;
                    }

                    // Display the age
                    let ageDisplay = this.nextElementSibling;
                    if (!ageDisplay || !ageDisplay.classList.contains('text-info')) {
                        ageDisplay = document.createElement('small');
                        ageDisplay.className = 'text-info d-block mt-1';
                        this.parentNode.appendChild(ageDisplay);
                    }
                    ageDisplay.textContent = 'Current Age: ' + age + ' years old';

                    // Auto-check senior citizen checkbox if age >= 60
                    const seniorCheckbox = document.getElementById('is_senior');
                    if (seniorCheckbox && age >= 60) {
                        seniorCheckbox.checked = true;
                    }
                });
            }

            // Show session messages as toasts when page loads
            <?php if (isset($_SESSION['success'])): ?>
                showToast("<?php echo addslashes($_SESSION['success']); ?>", "success");
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                showToast("<?php echo addslashes($_SESSION['error']); ?>", "danger");
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['info'])): ?>
                showToast("<?php echo addslashes($_SESSION['info']); ?>", "info");
                <?php unset($_SESSION['info']); ?>
            <?php endif; ?>

            // Fix Active status badges immediately
            function fixActiveBadges() {
                console.log("Fixing Active badges...");

                // First, target all buttons with Active text
                document.querySelectorAll('button').forEach(function(button) {
                    if (button.textContent && button.textContent.includes('Active')) {
                        console.log("Found Active button:", button);
                        button.className = 'btn btn-success btn-sm active-badge';
                        button.style.cssText = `
                            background-color: #28a745 !important;
                            color: white !important;
                            border-color: #28a745 !important;
                        `;

                        // Make sure it has a checkmark
                        if (!button.innerHTML.includes('✅')) {
                            button.innerHTML = '✅ Active';
                        }
                    }
                });

                // Target all elements with the active-badge class
                document.querySelectorAll('.active-badge').forEach(function(badge) {
                    console.log("Found active-badge:", badge);
                    badge.style.cssText = `
                        background-color: #28a745 !important;
                        color: white !important;
                        border-color: #28a745 !important;
                    `;
                });

                // Target all elements with Active text
                document.querySelectorAll('*').forEach(function(element) {
                    if (element.textContent && element.textContent.trim() === 'Active') {
                        console.log("Found element with Active text:", element);
                        element.style.cssText = `
                            display: inline-block !important;
                            background-color: #28a745 !important;
                            color: white !important;
                            padding: 4px 10px !important;
                            border-radius: 5px !important;
                            border: 1px solid #28a745 !important;
                            font-size: 13px !important;
                            font-weight: 500 !important;
                        `;

                        // Make sure it has a checkmark
                        if (!element.innerHTML.includes('✅')) {
                            element.innerHTML = '✅ Active';
                        }
                    }
                });

                // Specifically target the table cells with Active badges
                document.querySelectorAll('table td').forEach(function(cell) {
                    if (cell.textContent && cell.textContent.includes('Active')) {
                        console.log("Found table cell with Active:", cell);
                        const buttons = cell.querySelectorAll('button');
                        buttons.forEach(function(button) {
                            button.className = 'btn btn-success btn-sm active-badge';
                            button.style.cssText = `
                                background-color: #28a745 !important;
                                color: white !important;
                                border-color: #28a745 !important;
                            `;

                            // Make sure it has a checkmark
                            if (!button.innerHTML.includes('✅')) {
                                button.innerHTML = '✅ Active';
                            }
                        });
                    }
                });
            }

            // Run immediately
            fixActiveBadges();

            // Also run after a small delay to catch any dynamically added elements
            setTimeout(fixActiveBadges, 100);

            // Run one more time after the page is fully loaded
            window.onload = function() {
                fixActiveBadges();
            };
        });

        function exportToExcel() {
            alert('Export to Excel functionality will be implemented soon');
        }

        // Function to show toast notifications
        function showToast(message, type = 'success') {
            // Get the toast element
            const toast = document.getElementById('successToast');
            const toastMessage = document.getElementById('toast-message');
            const toastIcon = document.getElementById('toast-icon');

            // Set message
            toastMessage.textContent = message;

            // Set icon and styling based on message type
            if (type === 'success') {
                toast.style.backgroundColor = '#28a745';
                toastIcon.textContent = '✅';
            } else if (type === 'danger' || type === 'error') {
                toast.style.backgroundColor = '#dc3545';
                toastIcon.textContent = '❌';
            } else if (type === 'warning') {
                toast.style.backgroundColor = '#ffc107';
                toast.style.color = '#000';
                toastIcon.textContent = '⚠️';
            } else {
                toast.style.backgroundColor = '#17a2b8';
                toastIcon.textContent = '📝';
            }

            // Set text color
            toast.style.color = (type === 'warning') ? '#000' : 'white';

            // Show the toast
            const successToast = new bootstrap.Toast(toast, {
                delay: 5000
            });
            successToast.show();
        }
</script>
</body>
</html>